💻 Server listening on port 8000
💳  Step 1 : Paste this URL in your browser : http://fc2squickbooks.yobo.dev:8000
💳  Step 2 : <PERSON><PERSON> and Paste the clientId and clientSecret from : https://developer.intuit.com
💳  Step 3 : Copy Paste this callback URL into redirectURI :http://localhost:8000/callback
💻  Step 4 : Make Sure this redirect URI is also listed under the Redirect URIs on your app in : https://developer.intuit.com
[
  {
    Name: 'AE_Casey',
    SubClass: true,
    ParentRef: { value: '1900000000000970041' },
    FullyQualifiedName: 'Direct Support:AE_Casey',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970059',
    SyncToken: '0',
    MetaData: {
      CreateTime: '2023-01-04T13:45:10-08:00',
      LastUpdatedTime: '2023-01-04T13:45:10-08:00'
    }
  },
  {
    Name: 'Aim Higher (Interns)',
    SubClass: false,
    FullyQualifiedName: 'Aim Higher (Interns)',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970037',
    SyncToken: '0',
    MetaData: {
      CreateTime: '2023-01-04T13:45:09-08:00',
      LastUpdatedTime: '2023-01-04T13:45:09-08:00'
    }
  },
  {
    Name: 'AL ETV',
    SubClass: true,
    ParentRef: { value: '1900000000000970035' },
    FullyQualifiedName: 'State ETV:AL ETV',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970040',
    SyncToken: '0',
    MetaData: {
      CreateTime: '2023-01-04T13:45:09-08:00',
      LastUpdatedTime: '2023-01-04T13:45:09-08:00'
    }
  },
  {
    Name: 'AL_CAA',
    SubClass: true,
    ParentRef: { value: '1900000000000970061' },
    FullyQualifiedName: 'Direct Support:CAA - Appropriations Act:AL_CAA',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970086',
    SyncToken: '4',
    MetaData: {
      CreateTime: '2023-01-04T13:45:10-08:00',
      LastUpdatedTime: '2024-01-26T04:54:10-08:00'
    }
  },
  {
    Name: 'ASP (Vmentor student Success)',
    SubClass: false,
    FullyQualifiedName: 'ASP (Vmentor student Success)',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970033',
    SyncToken: '0',
    MetaData: {
      CreateTime: '2023-01-04T13:45:09-08:00',
      LastUpdatedTime: '2023-01-04T13:45:10-08:00'
    }
  },
  {
    Name: 'AZ ETV',
    SubClass: true,
    ParentRef: { value: '1900000000000970035' },
    FullyQualifiedName: 'State ETV:AZ ETV',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970052',
    SyncToken: '0',
    MetaData: {
      CreateTime: '2023-01-04T13:45:10-08:00',
      LastUpdatedTime: '2023-01-04T13:45:10-08:00'
    }
  },
  {
    Name: 'AZ_CAA',
    SubClass: true,
    ParentRef: { value: '1900000000000970061' },
    FullyQualifiedName: 'Direct Support:CAA - Appropriations Act:AZ_CAA',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970080',
    SyncToken: '4',
    MetaData: {
      CreateTime: '2023-01-04T13:45:10-08:00',
      LastUpdatedTime: '2024-01-26T04:54:27-08:00'
    }
  },
  {
    Name: 'Burtrez Morrow Loan',
    SubClass: true,
    ParentRef: { value: '1900000000000970041' },
    FullyQualifiedName: 'Direct Support:Burtrez Morrow Loan',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970049',
    SyncToken: '0',
    MetaData: {
      CreateTime: '2023-01-04T13:45:10-08:00',
      LastUpdatedTime: '2023-01-04T13:45:10-08:00'
    }
  },
  {
    Name: 'CAA - Appropriations Act',
    SubClass: true,
    ParentRef: { value: '1900000000000970041' },
    FullyQualifiedName: 'Direct Support:CAA - Appropriations Act',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970061',
    SyncToken: '4',
    MetaData: {
      CreateTime: '2023-01-04T13:45:10-08:00',
      LastUpdatedTime: '2024-01-26T04:53:53-08:00'
    }
  },
  {
    Name: 'CAA_SUP',
    SubClass: false,
    FullyQualifiedName: 'CAA_SUP',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '502632',
    SyncToken: '0',
    MetaData: {
      CreateTime: '2024-05-30T12:45:57-07:00',
      LastUpdatedTime: '2024-05-30T12:45:57-07:00'
    }
  },
  {
    Name: 'Care Packages',
    SubClass: false,
    FullyQualifiedName: 'Care Packages',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970036',
    SyncToken: '0',
    MetaData: {
      CreateTime: '2023-01-04T13:45:09-08:00',
      LastUpdatedTime: '2023-01-04T13:45:09-08:00'
    }
  },
  {
    Name: 'CARS',
    SubClass: false,
    FullyQualifiedName: 'CARS',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970062',
    SyncToken: '0',
    MetaData: {
      CreateTime: '2023-01-04T13:45:10-08:00',
      LastUpdatedTime: '2023-01-04T13:45:10-08:00'
    }
  },
  {
    Name: 'Casey',
    SubClass: true,
    ParentRef: { value: '1900000000000970041' },
    FullyQualifiedName: 'Direct Support:Casey',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970079',
    SyncToken: '0',
    MetaData: {
      CreateTime: '2023-01-04T13:45:10-08:00',
      LastUpdatedTime: '2023-01-04T13:45:10-08:00'
    }
  },
  {
    Name: 'Casey Graduate',
    SubClass: true,
    ParentRef: { value: '1900000000000970079' },
    FullyQualifiedName: 'Direct Support:Casey:Casey Graduate',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970084',
    SyncToken: '0',
    MetaData: {
      CreateTime: '2023-01-04T13:45:10-08:00',
      LastUpdatedTime: '2023-01-04T13:45:10-08:00'
    }
  },
  {
    Name: 'CCE',
    SubClass: true,
    ParentRef: { value: '1900000000000970041' },
    FullyQualifiedName: 'Direct Support:CCE',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970078',
    SyncToken: '0',
    MetaData: {
      CreateTime: '2023-01-04T13:45:10-08:00',
      LastUpdatedTime: '2023-01-04T13:45:10-08:00'
    }
  },
  {
    Name: 'CEJT',
    SubClass: true,
    ParentRef: { value: '1900000000000970041' },
    FullyQualifiedName: 'Direct Support:CEJT',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970058',
    SyncToken: '0',
    MetaData: {
      CreateTime: '2023-01-04T13:45:10-08:00',
      LastUpdatedTime: '2024-05-30T12:44:54-07:00'
    }
  },
  {
    Name: 'CFSA',
    SubClass: true,
    ParentRef: { value: '1900000000000970041' },
    FullyQualifiedName: 'Direct Support:CFSA',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970077',
    SyncToken: '0',
    MetaData: {
      CreateTime: '2023-01-04T13:45:10-08:00',
      LastUpdatedTime: '2023-01-04T13:45:10-08:00'
    }
  },
  {
    Name: 'CO ETV',
    SubClass: true,
    ParentRef: { value: '1900000000000970035' },
    FullyQualifiedName: 'State ETV:CO ETV',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970042',
    SyncToken: '0',
    MetaData: {
      CreateTime: '2023-01-04T13:45:10-08:00',
      LastUpdatedTime: '2023-01-04T13:45:10-08:00'
    }
  },
  {
    Name: 'Community Now',
    SubClass: false,
    FullyQualifiedName: 'Community Now',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000001346234',
    SyncToken: '0',
    MetaData: {
      CreateTime: '2023-08-21T12:09:35-07:00',
      LastUpdatedTime: '2023-08-21T12:09:35-07:00'
    }
  },
  {
    Name: 'CO_CAA',
    SubClass: true,
    ParentRef: { value: '1900000000000970061' },
    FullyQualifiedName: 'Direct Support:CAA - Appropriations Act:CO_CAA',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970083',
    SyncToken: '4',
    MetaData: {
      CreateTime: '2023-01-04T13:45:10-08:00',
      LastUpdatedTime: '2024-01-26T04:54:38-08:00'
    }
  },
  {
    Name: 'Direct Support',
    SubClass: false,
    FullyQualifiedName: 'Direct Support',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970041',
    SyncToken: '0',
    MetaData: {
      CreateTime: '2023-01-04T13:45:09-08:00',
      LastUpdatedTime: '2024-02-29T11:20:14-08:00'
    }
  },
  {
    Name: 'Duke',
    SubClass: true,
    ParentRef: { value: '1900000000000970033' },
    FullyQualifiedName: 'ASP (Vmentor student Success):Duke',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970047',
    SyncToken: '0',
    MetaData: {
      CreateTime: '2023-01-04T13:45:10-08:00',
      LastUpdatedTime: '2023-01-04T13:45:10-08:00'
    }
  },
  {
    Name: 'FosterU',
    SubClass: false,
    FullyQualifiedName: 'FosterU',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970054',
    SyncToken: '0',
    MetaData: {
      CreateTime: '2023-01-04T13:45:10-08:00',
      LastUpdatedTime: '2023-01-04T13:45:10-08:00'
    }
  },
  {
    Name: 'G&A',
    SubClass: false,
    FullyQualifiedName: 'G&A',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970034',
    SyncToken: '0',
    MetaData: {
      CreateTime: '2023-01-04T13:45:09-08:00',
      LastUpdatedTime: '2023-01-04T13:45:10-08:00'
    }
  },
  {
    Name: 'HENRY',
    SubClass: true,
    ParentRef: { value: '1900000000000970058' },
    FullyQualifiedName: 'Direct Support:CEJT:HENRY',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '502629',
    SyncToken: '0',
    MetaData: {
      CreateTime: '2024-05-30T12:43:42-07:00',
      LastUpdatedTime: '2024-05-30T12:43:42-07:00'
    }
  },
  {
    Name: 'JIM',
    SubClass: true,
    ParentRef: { value: '1900000000000970058' },
    FullyQualifiedName: 'Direct Support:CEJT:JIM',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '502631',
    SyncToken: '0',
    MetaData: {
      CreateTime: '2024-05-30T12:44:54-07:00',
      LastUpdatedTime: '2024-05-30T12:44:54-07:00'
    }
  },
  {
    Name: 'JKC',
    SubClass: true,
    ParentRef: { value: '1900000000000970041' },
    FullyQualifiedName: 'Direct Support:JKC',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970051',
    SyncToken: '0',
    MetaData: {
      CreateTime: '2023-01-04T13:45:10-08:00',
      LastUpdatedTime: '2023-01-04T13:45:10-08:00'
    }
  },
  {
    Name: 'Marg',
    SubClass: true,
    ParentRef: { value: '1900000000000970058' },
    FullyQualifiedName: 'Direct Support:CEJT:Marg',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970072',
    SyncToken: '1',
    MetaData: {
      CreateTime: '2023-01-04T13:45:10-08:00',
      LastUpdatedTime: '2024-05-30T12:43:55-07:00'
    }
  },
  {
    Name: 'MD ETV',
    SubClass: true,
    ParentRef: { value: '1900000000000970035' },
    FullyQualifiedName: 'State ETV:MD ETV',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970046',
    SyncToken: '0',
    MetaData: {
      CreateTime: '2023-01-04T13:45:10-08:00',
      LastUpdatedTime: '2023-01-04T13:45:10-08:00'
    }
  },
  {
    Name: 'MD_CAA',
    SubClass: true,
    ParentRef: { value: '1900000000000970061' },
    FullyQualifiedName: 'Direct Support:CAA - Appropriations Act:MD_CAA',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970087',
    SyncToken: '4',
    MetaData: {
      CreateTime: '2023-01-04T13:45:10-08:00',
      LastUpdatedTime: '2024-01-26T04:54:50-08:00'
    }
  },
  {
    Name: 'MO ETV',
    SubClass: true,
    ParentRef: { value: '1900000000000970035' },
    FullyQualifiedName: 'State ETV:MO ETV',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970038',
    SyncToken: '0',
    MetaData: {
      CreateTime: '2023-01-04T13:45:09-08:00',
      LastUpdatedTime: '2023-01-04T13:45:09-08:00'
    }
  },
  {
    Name: 'MO Reach',
    SubClass: false,
    FullyQualifiedName: 'MO Reach',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970053',
    SyncToken: '0',
    MetaData: {
      CreateTime: '2023-01-04T13:45:10-08:00',
      LastUpdatedTime: '2023-01-04T13:45:10-08:00'
    }
  },
  {
    Name: 'MO Reach CCE',
    SubClass: true,
    ParentRef: { value: '1900000000000970053' },
    FullyQualifiedName: 'MO Reach:MO Reach CCE',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970057',
    SyncToken: '0',
    MetaData: {
      CreateTime: '2023-01-04T13:45:10-08:00',
      LastUpdatedTime: '2023-01-04T13:45:10-08:00'
    }
  },
  {
    Name: 'MO_CAA',
    SubClass: true,
    ParentRef: { value: '1900000000000970061' },
    FullyQualifiedName: 'Direct Support:CAA - Appropriations Act:MO_CAA',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970085',
    SyncToken: '4',
    MetaData: {
      CreateTime: '2023-01-04T13:45:10-08:00',
      LastUpdatedTime: '2024-01-26T04:54:44-08:00'
    }
  },
  {
    Name: 'NC ETV',
    SubClass: true,
    ParentRef: { value: '1900000000000970035' },
    FullyQualifiedName: 'State ETV:NC ETV',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970039',
    SyncToken: '0',
    MetaData: {
      CreateTime: '2023-01-04T13:45:09-08:00',
      LastUpdatedTime: '2023-01-04T13:45:09-08:00'
    }
  },
  {
    Name: 'NC Reach',
    SubClass: false,
    FullyQualifiedName: 'NC Reach',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970048',
    SyncToken: '0',
    MetaData: {
      CreateTime: '2023-01-04T13:45:10-08:00',
      LastUpdatedTime: '2023-01-04T13:45:10-08:00'
    }
  },
  {
    Name: 'NCReach_Emergency',
    SubClass: true,
    ParentRef: { value: '1900000000000970041' },
    FullyQualifiedName: 'Direct Support:NCReach_Emergency',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970074',
    SyncToken: '0',
    MetaData: {
      CreateTime: '2023-01-04T13:45:10-08:00',
      LastUpdatedTime: '2023-01-04T13:45:10-08:00'
    }
  },
  {
    Name: 'NC_CAA',
    SubClass: true,
    ParentRef: { value: '1900000000000970061' },
    FullyQualifiedName: 'Direct Support:CAA - Appropriations Act:NC_CAA',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970082',
    SyncToken: '4',
    MetaData: {
      CreateTime: '2023-01-04T13:45:10-08:00',
      LastUpdatedTime: '2024-01-26T04:54:32-08:00'
    }
  },
  {
    Name: 'NY ETV',
    SubClass: true,
    ParentRef: { value: '1900000000000970035' },
    FullyQualifiedName: 'State ETV:NY ETV',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970044',
    SyncToken: '0',
    MetaData: {
      CreateTime: '2023-01-04T13:45:10-08:00',
      LastUpdatedTime: '2023-01-04T13:45:10-08:00'
    }
  },
  {
    Name: 'OCS',
    SubClass: false,
    FullyQualifiedName: 'OCS',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '500254',
    SyncToken: '0',
    MetaData: {
      CreateTime: '2024-05-23T10:31:56-07:00',
      LastUpdatedTime: '2024-05-23T10:31:56-07:00'
    }
  },
  {
    Name: 'OH ETV',
    SubClass: true,
    ParentRef: { value: '1900000000000970035' },
    FullyQualifiedName: 'State ETV:OH ETV',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970045',
    SyncToken: '0',
    MetaData: {
      CreateTime: '2023-01-04T13:45:10-08:00',
      LastUpdatedTime: '2023-01-04T13:45:10-08:00'
    }
  },
  {
    Name: 'OH_CAA',
    SubClass: true,
    ParentRef: { value: '1900000000000970061' },
    FullyQualifiedName: 'Direct Support:CAA - Appropriations Act:OH_CAA',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970081',
    SyncToken: '4',
    MetaData: {
      CreateTime: '2023-01-04T13:45:10-08:00',
      LastUpdatedTime: '2024-01-26T04:54:20-08:00'
    }
  },
  {
    Name: 'Scholar_Emergency',
    SubClass: true,
    ParentRef: { value: '1900000000000970041' },
    FullyQualifiedName: 'Direct Support:Scholar_Emergency',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970075',
    SyncToken: '0',
    MetaData: {
      CreateTime: '2023-01-04T13:45:10-08:00',
      LastUpdatedTime: '2023-01-04T13:45:10-08:00'
    }
  },
  {
    Name: 'Scholar_Emergencyholar',
    SubClass: true,
    ParentRef: { value: '1900000000000970041' },
    FullyQualifiedName: 'Direct Support:Scholar_Emergencyholar',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000001738981',
    SyncToken: '0',
    MetaData: {
      CreateTime: '2024-02-29T11:20:14-08:00',
      LastUpdatedTime: '2024-02-29T11:20:14-08:00'
    }
  },
  {
    Name: 'Sponsored',
    SubClass: true,
    ParentRef: { value: '1900000000000970041' },
    FullyQualifiedName: 'Direct Support:Sponsored',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970073',
    SyncToken: '0',
    MetaData: {
      CreateTime: '2023-01-04T13:45:10-08:00',
      LastUpdatedTime: '2023-01-04T13:45:10-08:00'
    }
  },
  {
    Name: 'State ETV',
    SubClass: false,
    FullyQualifiedName: 'State ETV',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970035',
    SyncToken: '0',
    MetaData: {
      CreateTime: '2023-01-04T13:45:09-08:00',
      LastUpdatedTime: '2023-05-17T12:38:00-07:00'
    }
  },
  {
    Name: 'TW_MO',
    SubClass: false,
    FullyQualifiedName: 'TW_MO',
    Active: true,
    domain: 'QBO',
    sparse: false,
    Id: '1900000000000970076',
    SyncToken: '0',
    MetaData: {
      CreateTime: '2023-01-04T13:45:10-08:00',
      LastUpdatedTime: '2023-01-04T13:45:10-08:00'
    }
  }
]
