'use strict';

require('dotenv').config();
const fs = require('fs');
const request = require('request');
const csv = require('csv-parser');
const path = require('path');

/**
 * Require the dependencies
 * @type {*|createApplication}
 */
const express = require('express');
const fileUpload = require('express-fileupload');

const app = express();
const OAuthClient = require('intuit-oauth');
const bodyParser = require('body-parser');
const ngrok = process.env.NGROK_ENABLED === 'true' ? require('ngrok') : null;

/**
 * Configure View and Handlebars
 */
app.use(bodyParser.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, '/public')));
app.use(fileUpload());
app.engine('html', require('ejs').renderFile);

app.set('view engine', 'html');
app.use(bodyParser.json());

const urlencodedParser = bodyParser.urlencoded({ extended: false });

/**
 * App Variables
 * @type {null}
 */
let oauth2_token_json = null;
let redirectUri = '';

/**
 * Instantiate new Client
 * @type {OAuthClient}
 */

let oauthClient = null;

/**
 * Home Route
 */
app.get('/', function (req, res) {
  res.render('index');
});

/**
 * Get the AuthorizeUri
 */



app.get('/set2023classes', async function (req, res) {
  const companyID = oauthClient.getToken().realmId;
  const url = oauthClient.environment === 'sandbox'
    ? OAuthClient.environment.sandbox
    : OAuthClient.environment.production;
  const minorversion = 70;

  const achToCheckMapping = {};
  const checkToProgMapping = {};
  const progToClassMapping = {};

  // Load CSV data into mappings
  async function loadCsvData() {
    const csvPaths = {
      achToCheck: path.resolve(__dirname, 'ach_to_check.csv'), // Update with the correct path to your CSV file
      checkToProg: path.resolve(__dirname, 'check_to_prog.csv'), // Update with the correct path to your CSV file
      progToClass: path.resolve(__dirname, 'prog_to_class.csv') // Update with the correct path to your CSV file
    };

    // Load ACH to CHECK mapping
    await new Promise((resolve, reject) => {
      fs.createReadStream(csvPaths.achToCheck)
        .pipe(csv())
        .on('data', (row) => {
          achToCheckMapping[row.ACH_ID] = row.CHECK_ID; // Adjust based on your CSV column names
        })
        .on('end', resolve)
        .on('error', reject);
    });

    // Load CHECK to PROG mapping
    await new Promise((resolve, reject) => {
      fs.createReadStream(csvPaths.checkToProg)
        .pipe(csv())
        .on('data', (row) => {
          checkToProgMapping[row.CHECK_ID] = row.PROG_CODE; // Adjust based on your CSV column names
        })
        .on('end', resolve)
        .on('error', reject);
    });

    // Load PROG to CLASS mapping
    await new Promise((resolve, reject) => {
      fs.createReadStream(csvPaths.progToClass)
        .pipe(csv())
        .on('data', (row) => {
          progToClassMapping[row.PROG_CODE] = row.CLASS; // Ensure the class ID is treated as a number
        })
        .on('end', resolve)
        .on('error', reject);
    });
  }

  async function fetchPurchases() {
    const query = `select * from Purchase where TxnDate > '2024-06-01' and TxnDate < '2024-07-01'`;
    const response = await oauthClient.makeApiCall({
      url: `${url}v3/company/${companyID}/query?query=${encodeURIComponent(query)}&minorversion=${minorversion}`,
      headers: {
        'Content-Type': 'application/text'
      }
    });

    const purchases = JSON.parse(response.text()).QueryResponse.Purchase || [];
    return purchases;
  }

  function extractAchId(description) {
    const match = description.match(/\b[A-Z0-9]{9}\b/);
    return match ? match[0] : null;
  }

  try {
    await loadCsvData();
    const purchases = await fetchPurchases();
    const classSummary = {
      missingClass: [],
      classCounts: {},
      errors: []
    };
    const purchasesToUpdate = [];

    purchases.forEach(purchase => {
      let purchaseUpdated = false;
      purchase.Line.forEach(line => {
        if (line.DetailType === 'AccountBasedExpenseLineDetail' && line.AccountBasedExpenseLineDetail.AccountRef) {
          if (!line.AccountBasedExpenseLineDetail.ClassRef || line.AccountBasedExpenseLineDetail.ClassRef.value === '1900000000000970000') {
            const achId = extractAchId(line.Description || purchase.PrivateNote);
            if (achId && achToCheckMapping[achId]) {
              const checkId = achToCheckMapping[achId];
              const progCode = checkToProgMapping[checkId];
              const classRef = progToClassMapping[progCode];
              console.log(`ACH_ID: ${achId}, CHECK_ID: ${checkId}, PROG_CODE: ${progCode}, CLASS_REF: ${classRef}`); // Log intermediate values
              if (classRef) {
                line.AccountBasedExpenseLineDetail.ClassRef = {
                  value: classRef, // Ensure the class ID is treated as a string
                  name: progCode // Include the name for better identification
                };
                purchaseUpdated = true;
              } else {
                classSummary.missingClass.push(purchase);
              }
            } else {
              classSummary.missingClass.push(purchase);
            }
          } else {
            const classRef = line.AccountBasedExpenseLineDetail.ClassRef.value;
            if (!classSummary.classCounts[classRef]) {
              classSummary.classCounts[classRef] = 0;
            }
            classSummary.classCounts[classRef]++;
          }
        }
      });
      if (purchaseUpdated) {
        purchasesToUpdate.push(purchase);
      }
    });

    // Update purchases in QuickBooks
    for (const purchase of purchasesToUpdate) {
      const updatePurchasePayload = {
        ...purchase,
        sparse: true // only send fields that are being updated
      };

      console.log(`Updating purchase ID ${purchase.Id} with payload:`, JSON.stringify(updatePurchasePayload, null, 2)); // Log the POST request payload

      try {
        await oauthClient.makeApiCall({
          url: `${url}v3/company/${companyID}/purchase`,
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(updatePurchasePayload)
        });
      } catch (e) {
        console.error(`Error updating purchase ID ${purchase.Id}: ${e.message}`);
        classSummary.errors.push({ purchaseId: purchase.Id, error: e.message });
        throw e; // Rethrow the error after logging it
      }
    }

    res.send({
      classSummary: classSummary,
      purchasesToUpdate: purchasesToUpdate
    });
  } catch (e) {
    console.error(e);
    res.status(500).send(e);
  }
});


app.get('/authUri', urlencodedParser, function (req, res) {
  
  
  oauthClient = new OAuthClient({
    clientId: 'ABYQVcadItwlvvLLieKwhU42kMtHUgCbmO5GXVIjyDKIu8dQt6',
    clientSecret: '4o9EPNqxlj1xEPWGV5eHbPmxgMH5aQi9KedhSVHB',
    environment: req.query.json.environment,
    redirectUri: 'https://fc2squickbooks.yobo.dev/callback',
  });

  const authUri = oauthClient.authorizeUri({
    scope: [OAuthClient.scopes.Accounting],
    state: 'intuit-test',
  });
  res.send(authUri);
});



app.get('/setCheckClasses', async function (req, res) {
  const companyID = oauthClient.getToken().realmId;
  const url = oauthClient.environment === 'sandbox'
    ? OAuthClient.environment.sandbox
    : OAuthClient.environment.production;
  const minorversion = 65;

  const accountToClassMapping = {
    "Accounts Payable:ETV-NC Checks": '1900000000000970039',
    "Accounts Payable:ETV-MO Checks": '1900000000000970038',
    "Accounts Payable:FCS CEJT Payable": '1900000000000970058',
    "Accounts Payable:Scholarships Payable": '500254',
    "Accounts Payable:ETV-AL Checks": '1900000000000970040',
    "Accounts Payable:StudentServices Payable": '502652'
  };

  async function fetchAllPurchases() {
    let startPosition = 1;
    const maxResults = 1000; // You can adjust this number based on your preference
    let purchases = [];
    let moreData = true;

    while (moreData) {
      const query = `select * from purchase where TxnDate >= '2024-06-01' and TxnDate <= '2024-07-01' startPosition ${startPosition} maxResults ${maxResults}`;
      //const query = `select * from purchase where id= '222015' startPosition ${startPosition} maxResults ${maxResults}`;
      const response = await oauthClient.makeApiCall({
        url: `${url}v3/company/${companyID}/query?query=${encodeURIComponent(query)}&minorversion=${minorversion}`
      });

      const fetchedPurchases = JSON.parse(response.text()).QueryResponse.Purchase || [];
      purchases = purchases.concat(fetchedPurchases);
      if (fetchedPurchases.length < maxResults) {
        moreData = false;
      } else {
        startPosition += maxResults;
      }
    }

    return purchases;
  }

  try {
    const purchases = await fetchAllPurchases();
    const accountCounts = {};
    const purchasesToUpdate = [];

    purchases.forEach(purchase => {
      let purchaseUpdated = false;
      purchase.Line.forEach(line => {
        if (line.DetailType === 'AccountBasedExpenseLineDetail' && line.AccountBasedExpenseLineDetail.AccountRef) {
          const accountName = line.AccountBasedExpenseLineDetail.AccountRef.name;
          if (!accountCounts[accountName]) {
            accountCounts[accountName] = 0;
          }
          accountCounts[accountName]++;
          if (accountToClassMapping[accountName]) {
            line.AccountBasedExpenseLineDetail.ClassRef = {
              value: accountToClassMapping[accountName]
            };
            purchaseUpdated = true;
          }
        }
      });
      if (purchaseUpdated) {
        purchasesToUpdate.push(purchase);
      }
    });

    // Update purchases in QuickBooks
    for (const purchase of purchasesToUpdate) {
      const updatePurchasePayload = {
        ...purchase,
        sparse: true // only send fields that are being updated
      };

      await oauthClient.makeApiCall({
        url: `${url}v3/company/${companyID}/purchase`,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updatePurchasePayload)
      });
    }

    res.send({
      accountCounts: accountCounts,
      purchasesToUpdate: purchasesToUpdate
    });
  } catch (e) {
    console.error(e);
    res.status(500).send(e);
  }
});



app.get('/getClasslessPayments', async function (req, res) {
  const companyID = oauthClient.getToken().realmId;
  const url = oauthClient.environment === 'sandbox'
    ? OAuthClient.environment.sandbox
    : OAuthClient.environment.production;
  const minorversion = 65;

  const itemToClassMapping = {
    "Vouchers:CEJT MARG Routable Checks": { "value": '1900000000000970072' },
    "Vouchers:OCS": { "value": '500254' },
    "Vouchers:ETV-NC": { "value": '1900000000000970039' },
    "Vouchers:ETV-MO": { "value": '1900000000000970038' },
    "Vouchers:CEJT Henry Routable Checks": { "value": '502629' },
    "Vouchers:AL CAA_SUP": { "value": '502632' },
    "Vouchers:ETV-AL": { "value": '1900000000000970040' },
    "Vouchers:StudentServices": { "value": '502652' },
    "Vouchers:MO CCE": { "value": '1900000000000970057' },
    "Vouchers:CEJT Jim Routable Checks": { "value": '502631' },
    "Vouchers:ETV-MD": {"value": '1900000000000970046'},
    "Vouchers:ETV-OH": {"value": '1900000000000970045'},
    "Vouchers:ETV-AZ": {"value": '1900000000000970052'}
  };

  async function fetchAllBills() {
    let startPosition = 1;
    const maxResults = 1000; // You can adjust this number based on your preference
    let bills = [];
    let moreData = true;

    while (moreData) {
      const query = `select * from bill where TxnDate >= '2024-06-01' and TxnDate <= '2024-07-01' startPosition ${startPosition} maxResults ${maxResults}`;
      const response = await oauthClient.makeApiCall({
        url: `${url}v3/company/${companyID}/query?query=${encodeURIComponent(query)}&minorversion=${minorversion}`
      });

      const fetchedBills = JSON.parse(response.text()).QueryResponse.Bill || [];
      bills = bills.concat(fetchedBills);
      if (fetchedBills.length < maxResults) {
        moreData = false;
      } else {
        startPosition += maxResults;
      }
    }

    return bills;
  }

  try {
    const bills = await fetchAllBills();
    const billsWithClassRef = [];
    const billsWithoutClassRef = [];
    const itemCount = {};

    // Loop over bills to categorize them
    for (const bill of bills) {
      let hasClassRef = false;
      for (const line of bill.Line) {
        if ((line.ItemBasedExpenseLineDetail && line.ItemBasedExpenseLineDetail.ClassRef) ||
            (line.AccountBasedExpenseLineDetail && line.AccountBasedExpenseLineDetail.ClassRef)) {
          hasClassRef = true;
          break;
        }
      }

      if (hasClassRef) {
        billsWithClassRef.push(bill);
      } else {
        billsWithoutClassRef.push(bill);
        for (const line of bill.Line) {
          if (line.ItemBasedExpenseLineDetail && line.ItemBasedExpenseLineDetail.ItemRef) {
            const itemName = line.ItemBasedExpenseLineDetail.ItemRef.name;
            if (!itemCount[itemName]) {
              itemCount[itemName] = 0;
            }
            itemCount[itemName]++;
          }
        }
      }
    }

    // Update bills without class references with the proper class references based on item types
    for (const bill of billsWithoutClassRef) {
      let billUpdated = false;
      for (const line of bill.Line) {
        if (line.ItemBasedExpenseLineDetail && line.ItemBasedExpenseLineDetail.ItemRef) {
          const itemName = line.ItemBasedExpenseLineDetail.ItemRef.name;
          if (itemToClassMapping[itemName]) {
            line.ItemBasedExpenseLineDetail.ClassRef = itemToClassMapping[itemName];
            billUpdated = true;
          }
        }
      }

      if (billUpdated) {
        // Make API call to update the bill in QuickBooks
        const updateBillPayload = {
          ...bill,
          sparse: true // only send fields that are being updated
        };

        await oauthClient.makeApiCall({
          url: `${url}v3/company/${companyID}/bill`,
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(updateBillPayload)
        });
      }
    }

    res.send({
      billsWithClassRef: billsWithClassRef,
      billsWithoutClassRef: billsWithoutClassRef,
      itemCount: itemCount
    });

  } catch (e) {
    console.error(e);
    res.status(500).send(e);
  }
});




app.get('/getClasslessPayments3', async function (req, res) {
  const companyID = oauthClient.getToken().realmId;
  const url = oauthClient.environment === 'sandbox'
    ? OAuthClient.environment.sandbox
    : OAuthClient.environment.production;

  try {
    // Fetch all bills
    const billsResponse = await oauthClient.makeApiCall({
      url: `${url}v3/company/${companyID}/query?query=${encodeURIComponent('select * from bill')}&minorversion=65`
    });

    const bills = JSON.parse(billsResponse.text()).QueryResponse.Bill;
    const billsWithClassRef = [];
    const billsWithoutClassRef = [];
    const itemCount = {};

    // Mapping of items to class references
    const itemToClassMapping = {
      "Vouchers:CEJT MARG Routable Checks": { "value": '1900000000000970072' },
      "Vouchers:OCS": { "value": '500254' },
      "Vouchers:ETV-NC": { "value": '1900000000000970039' },
      "Vouchers:ETV-MO": { "value": '1900000000000970038' },
      "Vouchers:CEJT Henry Routable Checks": { "value": '502629' },
      "Vouchers:AL CAA_SUP": { "value": '502632' }
    };

    // Loop over bills to categorize them
    for (const bill of bills) {
      let hasClassRef = false;
      for (const line of bill.Line) {
        if ((line.ItemBasedExpenseLineDetail && line.ItemBasedExpenseLineDetail.ClassRef) ||
            (line.AccountBasedExpenseLineDetail && line.AccountBasedExpenseLineDetail.ClassRef)) {
          hasClassRef = true;
          break;
        }
      }

      if (hasClassRef) {
        billsWithClassRef.push(bill);
      } else {
        billsWithoutClassRef.push(bill);
        for (const line of bill.Line) {
          if (line.ItemBasedExpenseLineDetail && line.ItemBasedExpenseLineDetail.ItemRef) {
            const itemName = line.ItemBasedExpenseLineDetail.ItemRef.name;
            if (!itemCount[itemName]) {
              itemCount[itemName] = 0;
            }
            itemCount[itemName]++;
          }
        }
      }
    }

    // Update bills without class references with the proper class references based on item types
    for (const bill of billsWithoutClassRef) {
      let billUpdated = false;
      for (const line of bill.Line) {
        if (line.ItemBasedExpenseLineDetail && line.ItemBasedExpenseLineDetail.ItemRef) {
          const itemName = line.ItemBasedExpenseLineDetail.ItemRef.name;
          if (itemToClassMapping[itemName]) {
            line.ItemBasedExpenseLineDetail.ClassRef = itemToClassMapping[itemName];
            billUpdated = true;
          }
        }
      }

      if (billUpdated) {
        // Make API call to update the bill in QuickBooks
        const updateBillPayload = {
          ...bill,
          sparse: true // only send fields that are being updated
        };

        await oauthClient.makeApiCall({
          url: `${url}v3/company/${companyID}/bill`,
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(updateBillPayload)
        });
      }
    }

    res.send({
      billsWithClassRef: billsWithClassRef,
      billsWithoutClassRef: billsWithoutClassRef,
      itemCount: itemCount
    });

  } catch (e) {
    console.error(e);
    res.status(500).send(e);
  }
});


app.get('/getChecks', urlencodedParser, function (req, resp) {
  
  request('https://adm.fc2sprograms.org/', function(err, res, body) {
  
      let regexp = /(\/login\/[a-zA-Z0-9]*)/g;
      let match = body.match(regexp);
      //console.log(body);
      
      
      const options = {
      url: 'https://adm.fc2sprograms.org'+match[0],
      form: {
      userID: 'ofa',
      password: 'f00manchu'
      }
      };
      request.post(options, function(err,res,body) {
      
         let regexp = /sessionID=client\/(\d+)\//g;
	 let match = body.match(regexp);
	 let session = match[0];
	 let cookie = res['headers']['set-cookie'][0];  
	 console.log(cookie.substring(7,40));
	 console.log(cookie);
	
	 let options = {
	   url:'https://adm.fc2sprograms.org/ems/ajax/get_exported_checks_without_check_number/'
+ session + "/cgi-bin/ems.pl?_=_&token=.alksdjfqe900qeb.kajsbdfoiu",
           headers: {
	      Cookie: "emssid="+cookie.substring(7,40),
	   }
	 };
	 
	 request(options, function(err,res,body) {
	 
             //resp.send(body);
	     let checks = JSON.parse(body);
	     //checks.forEach(check => matchToQuickbooks(check));
	     getThisYearsChecks(checks,session,cookie);
        })
      
      })
      
  });
  //resp.send("");
});

function matchToQuickbooks(check){

  const companyID = oauthClient.getToken().realmId;
  const url =
    oauthClient.environment == 'sandbox'
      ? OAuthClient.environment.sandbox
      : OAuthClient.environment.production;
  
  let name = check.SSN.replace('-','').replace('-','')+"/"+check['CHECK_TO_NAME'];
  console.log(`finding vendor: "${name}"`);
  
  
  let qbvendors = oauthClient
        .makeApiCall({ url:
          `${url}v3/company/${companyID}/query?query=select * from vendor where DisplayName = '${name.replace('&','%26')}'&minorversion=65` })
        .then(function (authResponse) {
         //console.log(`The response for API call is :${JSON.stringify(authResponse)}`);
	 //console.log(JSON.stringify(authResponse.body));
	 
	 
	  let response = JSON.parse(authResponse.text());
	           //console.log(response.QueryResponse.Vendor);
		   
	  if(response.QueryResponse.Vendor){
	      let vendor = response.QueryResponse.Vendor[0];
	      console.log(vendor);
	 
	      //getEmsMatchingCheck(vendor,check);
	  }
	 
   });
}

function getThisYearsChecks(checks,session,cookie){
   const companyID = oauthClient.getToken().realmId;
   const url =
    oauthClient.environment == 'sandbox'
      ? OAuthClient.environment.sandbox
      : OAuthClient.environment.production;
  //let accountArr = payment.trans.ACCNT.split(':');
  //`${url}v3/company/${companyID}/reports/TransactionListByVendor?vendor=${vendor.Id}&start_date=2022-12-01&end_date=2099-12-12&minorversion=65`
  let qs =  url + "v3/company/"+companyID+"/query?query=select * from Purchase where PaymentType = 'Check' startposition 1 maxresults 1000&minorversion=65";
  console.log(qs);
  oauthClient
    .makeApiCall({ url: qs})
    .then(function (authResponse) {
     //console.log(JSON.stringify(authResponse.response.body));
     
     let qbchecks = JSON.parse(authResponse.text());
     
     qbchecks = qbchecks.QueryResponse.Purchase;
     
     
     
     compareChecks(checks,qbchecks,session,cookie);
    });
}

function compareChecks(checks,qbchecks,session,cookie){    
    
    //console.log(JSON.stringify(qbchecks.filter(purchase => purchase.TotalAmt == 6200)));
    //console.log(JSON.stringify(checks.filter(check => check.CHECK_AMOUNT == 6200)));
    
    //qbchecks = qbchecks.filter(purchase => purchase.TotalAmt == 6200);
    //checks = checks.filter(check => check.CHECK_AMOUNT == 6200);
    //let check = checks[0];
    
    
    checks.forEach( check => {
       let name = check.SSN.replace('-','').replace('-','')+"/"+check['CHECK_TO_NAME'];
       name = name.substring(0,38).toLowerCase().trim();
       let qbpayments = qbchecks.filter(payment => payment.TotalAmt == check.CHECK_AMOUNT);
       qbpayments = qbpayments.filter(payment => {
        if('EntityRef' in payment){
	    return payment.EntityRef.name.toLowerCase() == name
        }
	else{
	    return false;
	}
       });
       
       if(qbpayments.length ==1 && qbpayments[0].DocNumber){
         console.log('updating "'+check.CHECK_ID +'",'+qbpayments[0].DocNumber+',' );
	 
	 	 let options = {
	   url:'https://adm.fc2sprograms.org/ems/ajax/update_check_number/'
+ session +
"/cgi-bin/ems.pl?_=_&token=.alksdjfqe900qeb.kajsbdfoiu&check_id="
+check.CHECK_ID+"&check_number="+qbpayments[0].DocNumber,
           headers: {
	      Cookie: "emssid="+cookie.substring(7,40),
	   }
	 };
	 
	 request(options, function(err,res,body) {
	     console.log('updated "'+check.CHECK_ID +'",'+qbpayments[0].DocNumber+',' );
	     console.log(body);
         });
	 
	 
       }
       //console.log(qbpayments);
    });
    
    
    //console.log(qbchecks[0].EntityRef.name.toLowerCase());
    
    //console.log(JSON.stringify(qbchecks.filter(purchase => purchase.PaymentType == 'Check')));
    //qbchecks = qbchecks.filter(check => check.PrintStatus =='PrintComplete');
    //qbchecks = qbchecks.filter(check => check.Id == 208336);
    //console.log(qbchecks[0]);
    //qbchecks.forEach(payment => console.log(payment));
    
    //checks = checks.filter(chk => chk.);
    //let check = checks[0];
    //let name = check.SSN.replace('-','').replace('-','')+"/"+check['CHECK_TO_NAME'];
    //console.log(name);
    //console.log(qbchecks.filter(payment => payment.EntityRef.name.toLowerCase() == name.toLowerCase()));
}

function getEmsMatchingCheck(vendor,check){
   const companyID = oauthClient.getToken().realmId;
   const url =
    oauthClient.environment == 'sandbox'
      ? OAuthClient.environment.sandbox
      : OAuthClient.environment.production;
  //let accountArr = payment.trans.ACCNT.split(':');
  //`${url}v3/company/${companyID}/reports/TransactionListByVendor?vendor=${vendor.Id}&start_date=2022-12-01&end_date=2099-12-12&minorversion=65`

  let qs =  url + "v3/company/"+companyID+"/query?query=select * from Payment where CustomerRef = '"+vendor.Id+"'&minorversion=65";
  console.log(qs);
  oauthClient
    .makeApiCall({ url: qs})
    .then(function (authResponse) {
     console.log(JSON.stringify(authResponse.response.body)); 
    
    
//      let report = JSON.parse(authResponse.text());
//     console.log(JSON.stringify(report)); 
      
     
})}


/**
 * Handle the callback to extract the `Auth Code` and exchange them for `Bearer-Tokens`
 */
app.get('/callback', function (req, res) {
  oauthClient
    .createToken(req.url)
    .then(function (authResponse) {
      oauth2_token_json = JSON.stringify(authResponse.getJson(), null, 2);
    })
    .catch(function (e) {
      console.error(e);
    });
  
  res.send('');
});

/**
 * Display the token : CAUTION : JUST for sample purposes
 */
app.get('/retrieveToken', function (req, res) {
  const companyID = oauthClient.getToken().realmId;
  console.log(companyID);
  res.send(oauth2_token_json);
});


app.post('/uploadIIF',function (req,res) {
  const allFileContents = req.files.files.data.toString();
  
  let data = [];
  let type = "";
  let accounts = [];
  let vender = {};
  let transaction = {};
  
  allFileContents.split(/\r?\n/).forEach(line =>  {
    
    if(line.match(/^VEND/g)){
      //data.push(line);
      //data.push(line.match(/^VEND\t([^\t]*)\t([^\t]*)\t([^\t]*)\t([^\t]*)\t([^\t]*)\t([^\t]*)\t([^\t]*)\t([^\t]*)\t([^\t]*)/g));
      //data.push(line.split('\t'));
      let vendArr = line.split('\t');
      vender = {
        NAME: vendArr[1].trim(),
	PRINTAS: vendArr[2],
	ADDR1: vendArr[4],
	ADDR2: vendArr[5],
	ADDR3: vendArr[6],
	ADDR4: vendArr[7],
	VTYPE: vendArr[8],
	EMAIL: vendArr[9]
      };
      //data.push(vender);
    }
    //data.push(line.match(regex));
    else if (line.match(/^TRNS/g)){
       let trnsArr = line.split('\t');
       let trans = {
           TRNSTYPE: trnsArr[2],
	   DATE: trnsArr[3],
	   ACCNT: trnsArr[4],
	   NAME: trnsArr[5].trim(),
	   CLASS: trnsArr[6],
	   AMOUNT: trnsArr[7],
	   DOCNUM: trnsArr[8],
	   MEMO: trnsArr[9],
	   CLEAR: trnsArr[10],
	   TOPRINT: trnsArr[11],
	   ADDR5: trnsArr[12],
	   DUEDATE: trnsArr[13],
	   TERMS: trnsArr[14]
       };
       //vender.TRNS = trnsArr;
       vender.trans = trans;
       //data.push(vender);
    }
    else if (line.match(/^SPL/g)){
        let splArr = line.split('\t');
	let spl = {
	SPLID: splArr[1],
	TRNSTYPE: splArr[2],
	DATE: splArr[3],
	ACCNT: splArr[4],
	NAME: splArr[5].trim(),
	CLASS: splArr[6],
	AMOUNT: splArr[7],
	DOCNUM: splArr[8],
	MEMO: splArr[9],
	CLEAR: splArr[10],
	QNTY: splArr[11],
	REIMBEXP: splArr[12],
	SERVICE: splArr[13],
	DATE: splArr[14],
	OTHER2: splArr[15],
	};
	vender.spl = spl;
    }
    else if (line.match(/^ENDTRNS/g)){
       data.push(vender);
    }
  });
  
  
  data.forEach(payment => getVendor(payment));
  
  res.send(data);
});


//getVendor called by uploadIIF
function getVendor(payment){
  const companyID = oauthClient.getToken().realmId;
  const url =
    oauthClient.environment == 'sandbox'
      ? OAuthClient.environment.sandbox
      : OAuthClient.environment.production;
  
      console.log(`finding vendor: "${payment.NAME}"`);
      let qbvendors = oauthClient
        .makeApiCall({ url:
          `${url}v3/company/${companyID}/query?query=select * from vendor where DisplayName = '${payment.NAME.replace('&','%26')}'&minorversion=65` })
        .then(function (authResponse) {
         //console.log(`The response for API call is :${JSON.stringify(authResponse)}`);
	 let response = JSON.parse(authResponse.text());
	 //console.log(response.QueryResponse.Vendor);
	 
	 if(response.QueryResponse.Vendor){
             let vendor = response.QueryResponse.Vendor[0];
	     //
	     if(vendor.PrintOnCheckName == vendor.BillAddr.Line1){
	       //console.log(JSON.stringify(vendor));
	       updateVendorAddress(vendor);
	     }
	     
	     getAccount(payment,vendor);
	 }
	 else{
	     addVendor(payment);
	 }	 
	})
        .catch(function (e) {
	  console.log(payment.NAME);
          console.error(e);
       });
}

function updateVendorAddress(vendor){

  const companyID = oauthClient.getToken().realmId;
  const url =
    oauthClient.environment == 'sandbox'
      ? OAuthClient.environment.sandbox
      : OAuthClient.environment.production;
  
      let qbvendors = oauthClient
        .makeApiCall({ url:
          `${url}v3/company/${companyID}/vendor/{$vendor.Id}` })
        .then(function (authResponse) {
         //console.log(`The response for API call is :${JSON.stringify(authResponse)}`);
	 let response = JSON.parse(authResponse.text());
	 console.log(response.QueryResponse);
	 
	 	 
	})
        .catch(function (e) {
	  console.error(e);
       });

}

//called by getVendor
function addVendor(payment){
    
const companyID = oauthClient.getToken().realmId;

  const url =
    oauthClient.environment == 'sandbox'
      ? OAuthClient.environment.sandbox
      : OAuthClient.environment.production;

  let regex = /(.*)\W+([A-Z]{2})\W*(\d{5})/;
  let city,state,zip;
  let BillAddr = {};
  
  let addr_num = 1;
  
  let city_match = payment.ADDR1.match(regex);
  if(city_match){
    city = city_match[1];
    state = city_match[2];
    zip = city_match[3];
  }
  else{
      BillAddr['Line'+addr_num] = payment.ADDR1;
      addr_num = addr_num +1;
  }
  city_match = payment.ADDR2.match(regex);
  if(city_match){
    city = city_match[1];
    state = city_match[2];
    zip = city_match[3];
  }
  else{
      BillAddr['Line'+addr_num] = payment.ADDR2;
      addr_num = addr_num +1;
  }
  city_match = payment.ADDR3.match(regex);
  if(city_match){
    city = city_match[1];
    state = city_match[2];
    zip = city_match[3];
  }
  else{
      BillAddr['Line'+addr_num] = payment.ADDR3;
      addr_num = addr_num +1;
  }
  city_match = payment.ADDR4.match(regex);
  if(city_match){
    city = city_match[1];
    state = city_match[2];
    zip = city_match[3];
  }
  else{
      BillAddr['Line'+addr_num] = payment.ADDR4;
      addr_num = addr_num +1;
  }
  
  BillAddr['City'] = city;
  BillAddr['CountrySubDivisionCode'] = state;
  BillAddr['PostalCode'] = zip;
  //BillAddr['Line1'] = payment.NAME;

  let vendor_body = {
      "PrimaryEmailAddr": {
          "Address": payment.EMAIL,
      }, 
      "DisplayName": payment.NAME, 
      "CompanyName": payment.NAME,
      "BillAddr": BillAddr, 
      "PrintOnCheckName": payment.PRINTAS
  };
//  console.log(vendor);
//  console.log(vendor_body);
  console.log("adding vendor:" + payment.NAME);
  oauthClient
    .makeApiCall({ 
    url: `${url}v3/company/${companyID}/vendor?minorversion=65`,
    method: 'POST',
    body: vendor_body,
      
    })
    .then(function (authResponse) {
      //console.log(`The response for API call is :${JSON.stringify(authResponse)}`);
      //console.log(JSON.parse(authResponse.text()));
      //return JSON.parse(authResponse.text());
      getVendor(payment);
    })
    .catch(function (e) {
      console.log("failed to add vendor:" +payment.NAME);
      console.error(e);
    });
}



function getAccount(payment,vendor){
   const companyID = oauthClient.getToken().realmId;
   const url =
    oauthClient.environment == 'sandbox'
      ? OAuthClient.environment.sandbox
      : OAuthClient.environment.production;
  let accountArr = payment.trans.ACCNT.split(':');
  
  oauthClient
    .makeApiCall({ url:
`${url}v3/company/${companyID}/query?query=select * from account where name='${accountArr[1]}'&minorversion=65` })
    .then(function (authResponse) {
      //console.log(`The response for API call is :${JSON.stringify(authResponse)}`);
      let account_reply = JSON.parse(authResponse.text());
      if(account_reply.QueryResponse.Account[0]){
          //console.log(account_reply.QueryResponse.Account[0]);
	  let account= account_reply.QueryResponse.Account[0];
	  getPayment(payment,vendor,account)
      }
      else{
          console.log(accountArr);
      }
      //res.send(JSON.parse(authResponse.text()));
      
    })
    .catch(function (e) {
      console.error(e);
    });
}

function getPayment(payment,vendor,account){
   const companyID = oauthClient.getToken().realmId;
   const url =
    oauthClient.environment == 'sandbox'
      ? OAuthClient.environment.sandbox
      : OAuthClient.environment.production;
  let accountArr = payment.trans.ACCNT.split(':');
  oauthClient
    .makeApiCall({ url:
`${url}v3/company/${companyID}/reports/TransactionListByVendor?vendor=${vendor.Id}&start_date=2022-12-01&end_date=2099-12-12&minorversion=65`
})
    .then(function (authResponse) {
      let report = JSON.parse(authResponse.text());
      
      
      let payments = [];
      if(report.Rows.Row){
        let payment_rows = report.Rows.Row;
      
        payment_rows.forEach(row => {
      	  let list = row["Rows"]["Row"];
	  list.forEach( pay => {
	      let coldata = pay['ColData'];
	      payments.push({'date':coldata[0]['value'],'amt':coldata[6]['value']});
	    } 
	  )}
	  );
      }
      //console.log(payments);
      
      if(payments){
          
	  let real_payments = payments.filter(pay => {
	      let dateArr = payment.trans.DATE.split('/');
	      let date = `20${dateArr[2]}-${dateArr[0]}-${dateArr[1]}`;
	      let filter_amt = payment.trans.AMOUNT + ".00";
	      
	      if((pay.amt == filter_amt) && (pay.date == date)){
	          return true;
	      }
	      else return false;
	    }
          );
	  
	  //console.log(real_payments);
	  if(real_payments.length ==0){
	      console.log("create payment");
	      console.log(payment.NAME);
	      addPayment(payment,vendor,account);
	  }
	  else{
	      console.log("Duplicate");
	  }
      }
      else{
          console.log("new Payment");
	  addPayment(payment,vendor,account);
      }
      
    })
    .catch(function (e) {
      console.error(e);
    });
}

function addPayment(payment,vendor,account){
    const companyID = oauthClient.getToken().realmId;

  const url =
    oauthClient.environment == 'sandbox'
      ? OAuthClient.environment.sandbox
      : OAuthClient.environment.production;


   let dateArr = payment.trans.DATE.split('/');
  let date = `20${dateArr[2]}-${dateArr[0]}-${dateArr[1]}`;
  
  // "AccountRef": {
  //       "name": "1008 Wells Fargo Scholarships #2709", 
  //	 "value": "264"
  //    }, 
  //    "AccountRef": {
  //       "name": "1009 Wells Fargo ETV Program #1113", 
//	 "value": "272"
  //    }, 
     
  let AccountInfo = {
      "name": "1008 Wells Fargo Scholarships #2709",
      "value": "264"
  };
  
  
  if(account.Name.includes('ETV')){
      AccountInfo = {
        "name": "1009 Wells Fargo ETV Program #1113",
	"value": "272"
      };
  }
  
  let payment_body = {
      "PaymentType": "Check",
      "PrivateNote": payment.trans.MEMO,
      "PrintStatus": "NeedToPrint",
      "TxnDate": date,
      "EntityRef": {
          "value": vendor.Id,
	  "name": vendor.CompanyName,
	  "type": "Vendor"
      },
      "AccountRef": AccountInfo, 
      "Line": [
        {
	  "DetailType":	  "AccountBasedExpenseLineDetail", 
	  "Amount": payment.spl.AMOUNT, 
	  "AccountBasedExpenseLineDetail": {
	    "AccountRef": {
	      "name": account.Name, 
	      "value": account.Id
	    }
	  }
	}
      ]
      };
      
//    console.log(payment);
//    console.log(vendor);
//    console.log(account);
//    console.log(JSON.stringify(payment_body));
  oauthClient
    .makeApiCall({ 
    url: `${url}v3/company/${companyID}/purchase?minorversion=65`,
    method: 'POST',
    body: payment_body 
    })
    .then(function (authResponse) {
      //console.log(`The response for API call is :${JSON.stringify(authResponse)}`);
      console.log(JSON.parse(authResponse.text()));
        console.log("payment added for vendor:" + payment.NAME);
    })
    .catch(function (e) {
      console.error(e);
    });
}
app.get('/getVendorInfo', function (req, res) {
  const companyID = oauthClient.getToken().realmId;
  let displayName = req.query.name;
  const url =
    oauthClient.environment == 'sandbox'
      ? OAuthClient.environment.sandbox
      : OAuthClient.environment.production;

  oauthClient
    .makeApiCall({ url:
`${url}v3/company/${companyID}/query?query=select * from vendor where DisplayName = '${displayName}'&minorversion=65` })
    .then(function (authResponse) {
      console.log(`The response for API call is :${JSON.stringify(authResponse)}`);
      res.send(JSON.parse(authResponse.text()));
    })
    .catch(function (e) {
      console.error(e);
    });
});

/**
 * Refresh the access-token
 */
app.get('/refreshAccessToken', function (req, res) {
  oauthClient
    .refresh()
    .then(function (authResponse) {
      console.log(`The Refresh Token is  ${JSON.stringify(authResponse.getJson())}`);
      oauth2_token_json = JSON.stringify(authResponse.getJson(), null, 2);
      res.send(oauth2_token_json);
    })
    .catch(function (e) {
      console.error(e);
    });
});

/**
 * getCompanyInfo ()
 */
app.get('/getCompanyInfo', function (req, res) {
  const companyID = oauthClient.getToken().realmId;

  const url =
    oauthClient.environment == 'sandbox'
      ? OAuthClient.environment.sandbox
      : OAuthClient.environment.production;

  oauthClient
    .makeApiCall({ url: `${url}v3/company/${companyID}/companyinfo/${companyID}` })
    .then(function (authResponse) {
      console.log(`The response for API call is :${JSON.stringify(authResponse)}`);
      res.send(JSON.parse(authResponse.text()));
    })
    .catch(function (e) {
      console.error(e);
    });
});



app.get('/getCompanyPayments', function (req, res) {
  const companyID = oauthClient.getToken().realmId;

  const url =
    oauthClient.environment == 'sandbox'
      ? OAuthClient.environment.sandbox
      : OAuthClient.environment.production;

  oauthClient
    .makeApiCall({ url:
`${url}v3/company/${companyID}/query?query=select * from Purchase where PaymentType='Check'&minorversion=65` })
    .then(function (authResponse) {
      console.log(`The response for API call is :${JSON.stringify(authResponse)}`);
      res.send(JSON.parse(authResponse.text()));
    })
    .catch(function (e) {
      console.error(e);
    });
});




app.get('/addVendor', function (req, res) {
  const companyID = oauthClient.getToken().realmId;

  const url =
    oauthClient.environment == 'sandbox'
      ? OAuthClient.environment.sandbox
      : OAuthClient.environment.production;

  oauthClient
    .makeApiCall({ 
    url: `${url}v3/company/${companyID}/vendor?minorversion=65`,
    method: 'POST',
    body: 
      {
  "PrimaryEmailAddr": {
    "Address": "<EMAIL>"
  }, 
  "DisplayName": "xxxxx6696/NORTHERN ARIZONA UNIVERSITY", 
  "CompanyName": "xxxxx6696/NORTHERN ARIZONA UNIVERSITY", 
  "BillAddr": {
    "City": "FLAGSTAFF", 
    "Country": "U.S.A", 
    "Line3": "PO Box 4096", 
    "Line2": "Student Accounts", 
    "Line1": "NORTHERN ARIZONA UNIVERSITY", 
    "PostalCode": "86011", 
    "CountrySubDivisionCode": "AZ"
  }, 
  "PrintOnCheckName": "NORTHERN ARIZONA UNIVERSITY"
}
    })
    .then(function (authResponse) {
      console.log(`The response for API call is :${JSON.stringify(authResponse)}`);
      res.send(JSON.parse(authResponse.text()));
    })
    .catch(function (e) {
      console.error(e);
    });
});



app.get('/addPayment', function (req, res) {
  const companyID = oauthClient.getToken().realmId;

  const url =
    oauthClient.environment == 'sandbox'
      ? OAuthClient.environment.sandbox
      : OAuthClient.environment.production;

  oauthClient
    .makeApiCall({ 
    url: `${url}v3/company/${companyID}/purchase?minorversion=65`,
    method: 'POST',
    body: {
      "PaymentType": "Check",
      "PrivateNote": "Jordan Burch xxxxxx9999",
      "PrintStatus": "NeedToPrint",
      "EntityRef": {
          "value": "56",
	  "name": "Bob's Burger Joint",
	  "type":
	  "Vendor"
      },
      "AccountRef": {
         "name": "Checking", 
	 "value": "35"
      }, 
      "Line": [
        {
	  "DetailType":
	  "AccountBasedExpenseLineDetail", 
	  "Amount": 10.0, 
	  "AccountBasedExpenseLineDetail": {
	    "AccountRef": {
	      "name": "ETV-AL Vouchers", 
	      "value": "91"
	    }
	  }
	}
      ]
      }
    })
    .then(function (authResponse) {
      console.log(`The response for API call is :${JSON.stringify(authResponse)}`);
      res.send(JSON.parse(authResponse.text()));
    })
    .catch(function (e) {
      console.error(e);
    });
});



/**
 * disconnect ()
 */
app.get('/disconnect', function (req, res) {
  console.log('The disconnect called ');
  const authUri = oauthClient.authorizeUri({
    scope: [OAuthClient.scopes.OpenId, OAuthClient.scopes.Email],
    state: 'intuit-test',
  });
  res.redirect(authUri);
});

/**
 * Start server on HTTP (will use ngrok for HTTPS forwarding)
 */
const server = app.listen(process.env.PORT || 8000, () => {
  console.log(`💻 Server listening on port ${server.address().port}`);
  if (!ngrok) {
    redirectUri = 'https://fc2squickbooks.yobo.dev/callback';
    console.log(
      `💳  Step 1 : Paste this URL in your browser : ` +
        'http://fc2squickbooks.yobo.dev:' +
        `${server.address().port}`,
    );
    console.log(
      '💳  Step 2 : Copy and Paste the clientId and clientSecret from : https://developer.intuit.com',
    );
    console.log(
      `💳  Step 3 : Copy Paste this callback URL into redirectURI :` +
        'http://localhost:' +
        `${server.address().port}` +
        '/callback',
    );
    console.log(
      `💻  Step 4 : Make Sure this redirect URI is also listed under the Redirect URIs on your app in : https://developer.intuit.com`,
    );
  }
});

/**
 * Optional : If NGROK is enabled
 */
if (ngrok) {
  console.log('NGROK Enabled');
  ngrok
    .connect({ addr: process.env.PORT || 8000 })
    .then((url) => {
      redirectUri = `https://fc2squickbooks.yobo.dev/callback`;
      console.log(`💳 Step 1 : Paste this URL in your browser :  ${url}`);
      console.log(
        '💳 Step 2 : Copy and Paste the clientId and clientSecret from : https://developer.intuit.com',
      );
      console.log(`💳 Step 3 : Copy Paste this callback URL into redirectURI :  ${redirectUri}`);
      console.log(
        `💻 Step 4 : Make Sure this redirect URI is also listed under the Redirect URIs on your app in : https://developer.intuit.com`,
      );
    })
    .catch(() => {
      process.exit(1);
    });
}
