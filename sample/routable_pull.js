const axios = require('axios');
const createCsvWriter = require('csv-writer').createObjectCsvWriter;

// Function to fetch payables
async function fetchPayables(apiUrl, headers) {
  let payables = [];
  while (apiUrl) {
    try {
      const response = await axios.get(apiUrl, { headers });
      payables = payables.concat(response.data.results);
      apiUrl = response.data.links.next;
    } catch (error) {
      console.error(`Error fetching payables: ${error.message}`);
      break;
    }
  }
  return payables;
}

// Function to fetch payable details
async function fetchPayableDetails(apiUrl, headers) {
  try {
    const response = await axios.get(apiUrl, { headers });
    return response.data;
  } catch (error) {
    console.error(`Error fetching payable details: ${error.message}`);
    return null;
  }
}

// API credentials and URL
const apiUrl = 'https://api.routable.com/v1/payables?page=1&page_size=100&status=completed&';
const headers = {
  'accept': 'application/json',
  'authorization': 'Bearer b5d683706354c4f4a28a8c8c30cade7ecf8864b0'
};

// Main function to fetch data and create CSV file
async function main() {
  const payables = await fetchPayables(apiUrl, headers);
  const data = [];

  for (const payable of payables) {
    const payableDetails = await fetchPayableDetails(payable.links.self, headers);
    if (payableDetails) {
      data.push({
        display_name: payableDetails.pay_to_company.display_name,
        due_on: payableDetails.due_on,
        sent_on: payableDetails.sent_on,
        status: payableDetails.status,
        amount: payableDetails.amount,
        transaction_id: payableDetails.type_details.transaction_id,
        external_id: payableDetails.external_id
      });
    }
  }

  const csvWriter = createCsvWriter({
    path: 'payables.csv',
    header: [
      { id: 'display_name', title: 'Display Name' },
      { id: 'due_on', title: 'Due On' },
      { id: 'sent_on', title: 'Sent On' },
      { id: 'status', title: 'Status' },
      { id: 'amount', title: 'Amount' },
      { id: 'transaction_id', title: 'Transaction ID' },
      { id: 'external_id', title: 'External ID' }
    ]
  });

  await csvWriter.writeRecords(data);

  console.log('Data has been saved to payables.csv');
}

main();
