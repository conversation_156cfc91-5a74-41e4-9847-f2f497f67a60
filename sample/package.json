{"name": "intuit-nodejsclient", "version": "1.0.0", "description": "A sample NodeJs application to demonstrate the use of the client OAuth library", "scripts": {"start": "node app", "test": "./node_modules/mocha/bin/mocha test/**/*-test.js --reporter spec", "snyk-protect": "snyk protect", "prepublish": "npm run snyk-protect"}, "author": "<EMAIL>", "license": "APACHE", "homepage": "https://github.intuit.com/abisalehalliprasan/oauth-jsclient", "dependencies": {"axios": "^1.7.2", "body-parser": "latest", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0", "date-fns": "^3.6.0", "dotenv": "^5.0.1", "ejs": "^2.5.2", "express": "^4.14.0", "express-fileupload": "^1.4.0", "express-session": "^1.14.2", "intuit-oauth": "^3.0.1", "ngrok": "^3.2.5", "path": "^0.12.7", "xlsx": "^0.18.5"}, "devDependencies": {"snyk": "^1.316.1"}, "snyk": true}