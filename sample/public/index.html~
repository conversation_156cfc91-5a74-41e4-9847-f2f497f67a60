<!DOCTYPE html>
<html>
<head>
    <link rel="apple-touch-icon icon shortcut" type="image/png" href="https://plugin.intuitcdn.net/sbg-web-shell-ui/6.3.0/shell/harmony/images/QBOlogo.png">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/2.1.4/jquery.min.js"></script>
    <link rel="stylesheet" href="https://netdna.bootstrapcdn.com/bootstrap/3.1.1/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://netdna.bootstrapcdn.com/bootstrap/3.1.1/css/bootstrap-theme.min.css">
    <link rel="stylesheet" href="./css/common.css">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
</head>
<body>

<div class="container">

    <h1>
        <a href="http://developer.intuit.com">
            <img src="./images/quickbooks_logo_horz.png" id="headerLogo">
        </a>
    </h1>
    <hr>
    <div class="well text-center">
        <h1>FC2S Upload Checks</h1>
        <br>
    </div>
    <form>
        <div class="form-group">
            <label for="environment">Environment</label>
            <select id="environment" class="form-control">
            <option value="sandbox" selected="selected">Sandbox</option>
            <option value="production">Production</option>
            </select>
        </div>
        <div class="form-group" style="display: none">
            <label for="redirectUri">Redirect URI</label>
            <input type="text" class="form-control" value="https://fc2squickbooks.yobo.dev/callback"  id="redirectUri" /><br>
        </div>
	<div class="form-group" >
            <label for="emssession">emssession</label>
            <input type="text" class="form-control" value="" id="emssession" /><br>
        </div>
    </form>
    <p>Now click the <b>Connect to QuickBooks</b> button below.</p>
    <pre id="accessToken"></pre>
    <a class="imgLink" href="#" id="authorizeUri" ><img src="./images/C2QB_green_btn_lg_default.png" width="178" /></a>
    <button  type="button" id="retrieveToken" class="btn btn-success">Display Access Token</button>
    <button  type="button" id="refreshToken" class="btn btn-success" style="display: none">Refresh Token</button>
    <hr />

    <pre id="apiCall"></pre>
    <pre>IIF file upload</pre><input name="iif" id='iif' type="file">
    <button type="button" id="uploadIIF" class="btn btn-success">uploadIIF</button>
    <button type="button" id="getChecks" class="btn btn-success">matchChecks</button>
    
    <hr />
 <button type="button" id="getClasslessPayments" class="btn btn-success">Set Routable Classes</button>   
    <hr>
     <button type="button" id="setCheckClasses" class="btn btn-success">Set Check Classes</button>   
    <hr>

    <p class="text-center text-muted">
        &copy; 2018 Intuit&trade;, Inc. All rights reserved. Intuit and QuickBooks are registered trademarks of Intuit Inc.
    </p>

</div>

<script type="text/javascript">
    (function() {

        function authorizeUri() {

            // Generate the authUri
            var jsonBody = {};
//            jsonBody.clientId = $('#clientId').val();
//            jsonBody.clientSecret = $('#clientSecret').val();
            jsonBody.environment = $('#environment').val();
            jsonBody.redirectUri = $('#redirectUri').val();

            $.get('/authUri', {json:jsonBody}, function (uri) {
                console.log('The Auth Uris is :'+uri);
            })
            .then(function (authUri) {
                // Launch Popup using the JS window Object
                var parameters = "location=1,width=800,height=650";
                parameters += ",left=" + (screen.width - 800) / 2 + ",top=" + (screen.height - 650) / 2;
                var win = window.open(authUri, 'connectPopup', parameters);
                var pollOAuth = window.setInterval(function () {
                    try {
                        if (win.document.URL.indexOf("code") != -1) {
                            window.clearInterval(pollOAuth);
                            win.close();
                            location.reload();
                        }
                    } catch (e) {
                        console.log(e)
                    }
                }, 100);
            });
        }

        function retrieveToken() {

            // Generate the authUri
            $.get('/retrieveToken', function (token) {
                var token = (token!=null) ? token : 'Please Authorize Using Connect to Quickbooks first !';
                $("#accessToken").html(token);
            });
        }

        function refreshToken() {

            // Generate the authUri
            $.get('/refreshAccessToken', function (token) {
                var token = (token!=null) ? token : 'Please Authorize Using Connect to Quickbooks first !';
                $("#accessToken").html(token);
            });
        }

        function makeAPICall() {

            // Generate the authUri
            $.get('/getCompanyInfo', function (response) {
                $("#apiCall").html(JSON.stringify(response, null, 4));
            });
        }
	function parseIIF() {

            // Generate the authUri
            $.get('/parseIIF', function (response) {
                $("#apiCall").html(JSON.stringify(response, null, 4));
            });
        }
	function uploadIIfold() {

            var myFile = $('#iif').prop('files');
	    console.log("myfile");
	    console.log(myFile);
            $.post('/uploadIIF', {'data': {'file': myFile[0]}},function (response) {
                $("#apiCall").html(JSON.stringify(response, null, 4));
            });
        }
	function uploadIIF(){
	   var fd = new FormData();
	   var files = $('#iif')[0].files[0];
	   fd.append('files', files);
	   
	   $.ajax({
	   url: 'https://fc2squickbooks.yobo.dev/uploadIIF',
	   type: 'POST',
	   data: fd,
	   contentType: false,
	   processData: false,
	   success: function(response){
	       if(response != 0){
	           console.log(response);
		   alert('file uploaded');
	       }
	       else{
	           console.log(response);
	           alert('file not uploaded');
	       }
	   },
	   error: function(error){
	       console.log(error);
	   }
	   });
	};
	
	function makeAPICallPayments() {

            // Generate the authUri
            $.get('/getCompanyPayments', function (response) {
                $("#apiCall").html(JSON.stringify(response, null, 4));
            });
        }
	
	function addPayment() {

            // Generate the authUri
            $.get('/addPayment', function (response) {
                $("#apiCall").html(JSON.stringify(response, null, 4));
            });
        }
	function addVendor() {

            // Generate the authUri
            $.get('/addVendor', function (response) {
                $("#apiCall").html(JSON.stringify(response, null, 4));
            });
        }
	function getVendor() {

            // Generate the authUri
            $.get('/getVendorInfo?name=xxxxx6696/NORTHERN ARIZONA UNIVERSITY', function (response) {
                $("#apiCall").html(JSON.stringify(response, null, 4));
            });
        }
	
function getClasslessPayments()
        {

// Generate the authUri
$.get('/getClasslessPayments', function (response) {
    $("#apiCall").html(JSON.stringify(response, null, 4));
});
}

function setCheckClasses()
        {

// Generate the authUri
$.get('/setCheckClasses', function (response) {
    $("#apiCall").html(JSON.stringify(response, null, 4));
});
}


        function getChecks() {

            // Generate the authUri
            $.get('/getChecks', function (response) {
                $("#apiCall").html(JSON.stringify(response, null, 4));
            });
        }
	

        document.getElementById('getChecks').addEventListener('click', function response(e) {
            e.preventDefault();
            getChecks();
        });

        document.getElementById('authorizeUri').addEventListener('click', function response(e) {
            e.preventDefault();
            authorizeUri();
        });

        document.getElementById('retrieveToken').addEventListener('click', function response(e) {
            e.preventDefault();
            retrieveToken();
        });

        document.getElementById('refreshToken').addEventListener('click', function response(e) {
            e.preventDefault();
            refreshToken();
        });

	
        document.getElementById('uploadIIF').addEventListener('click', function response(e) {
            e.preventDefault();
            uploadIIF();
        });
	document.getElementById('getClasslessPayments').addEventListener('click', function response(e) {
            e.preventDefault();
            getClasslessPayments();
        });

document.getElementById('setCheckClasses').addEventListener('click', function response(e) {
            e.preventDefault();
            setCheckClasses();
        });


    })();
</script>
</body>
</html>
