{"extends": ["airbnb-base", "prettier"], "parserOptions": {"sourceType": "script"}, "rules": {"strict": ["error", "safe"], "prefer-object-spread": "off", "no-param-reassign": "off", "comma-dangle": ["error", {"arrays": "always-multiline", "objects": "always-multiline", "imports": "always-multiline", "exports": "always-multiline", "functions": "never"}], "no-underscore-dangle": "off", "no-unused-expressions": "off"}, "globals": {"sinon": true, "describe": true, "it": true, "expect": true, "test": true, "require": true}}